import { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  TouchableOpacity,
  Text,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { M1_V2_CONFIG } from '../config/config';
import { OrientationManager } from '../utils/orientationUtils';
import { authService } from '../services/AuthService';
import { Linking, Alert } from 'react-native';

const VideoPlayerScreen = ({ route, navigation }) => {
  const { videoIndex, allVideos, currentLanguage } = route.params;
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showBackButton, setShowBackButton] = useState(true);
  const webViewRef = useRef(null);

  // M1 V2 Player URL from configuration
  const playerUrl = M1_V2_CONFIG.URL;

  // Cleanup orientation when component unmounts
  useEffect(() => {
    return () => {
      // Reset orientation when leaving the screen
      OrientationManager.resetOrientation().catch(error => {
        console.warn('Failed to reset orientation on cleanup:', error.message);
      });
    };
  }, []);

  const handleWebViewMessage = async (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);

      switch (data.type) {
        case 'WEBVIEW_READY':
          if (webViewRef.current) {
            const currentUser = authService.getCurrentUser();
            if (currentUser?.token) {
              webViewRef.current.postMessage(JSON.stringify({
                type: 'SET_JWT_TOKEN',
                token: currentUser.token
              }));
            }
            webViewRef.current.postMessage(JSON.stringify({
              type: 'INITIALIZE_PLAYER',
              videoData: allVideos,
              startIndex: videoIndex
            }));
            webViewRef.current.postMessage(JSON.stringify({
              type: 'LANGUAGE_CHANGE',
              language: currentLanguage
            }));
          }
          break;
        case 'CONTACT': {
          const phone = (data.phone || '').trim();
          const email = (data.email || '').trim();
          try {
            if (phone) {
              const url = `tel:${phone}`;
              const supported = await Linking.canOpenURL(url);
              if (supported) { await Linking.openURL(url); return; }
            }
            if (email) {
              const url = `mailto:${email}`;
              const supported = await Linking.canOpenURL(url);
              if (supported) { await Linking.openURL(url); return; }
            }
            Alert.alert('Contact', phone || email ? `${phone || email}` : 'No contact info');
          } catch (e) {
            console.warn('Open contact failed:', e?.message || e);
          }
          break;
        }
        case 'FULLSCREEN_CHANGE': {
          const goingFullscreen = !!data.isFullscreen;
          setIsFullscreen(goingFullscreen);
          setShowBackButton(!goingFullscreen);
          try {
            if (goingFullscreen) {
              await OrientationManager.enterFullscreen();
            } else {
              await OrientationManager.exitFullscreen();
            }
          } catch (e) {
            console.warn('Orientation lock failed:', e?.message || e);
          }
          break;
        }
        case 'CONSOLE_LOG':
          if (__DEV__) {
            const level = data.level || 'log';
            const message = data.message || '';
            const logArgs = Array.isArray(data.args) ? data.args : [];
            // eslint-disable-next-line no-console
            console[level](`[WebView ${level.toUpperCase()}]`, message, ...logArgs);
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('Failed to parse WebView message:', error);
    }
  };

  const injectedJavaScript = __DEV__ ? `
    (function() {
      const originalConsole = { log: console.log, warn: console.warn, error: console.error, info: console.info, debug: console.debug };
      function sendLogToNative(level, args) {
        try {
          const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
          window.ReactNativeWebView?.postMessage(JSON.stringify({ type: 'CONSOLE_LOG', level: level, message: message, args: args }));
        } catch (e) {}
        originalConsole[level](...args);
      }
      console.log = (...args) => sendLogToNative('log', args);
      console.warn = (...args) => sendLogToNative('warn', args);
      console.error = (...args) => sendLogToNative('error', args);
      console.info = (...args) => sendLogToNative('info', args);
      console.debug = (...args) => sendLogToNative('debug', args);
      console.log('RN: WebView console override initialized.');
      true;
    })();
  ` : 'true;';

  const handleBackPress = () => {
    navigation.goBack();
  };

  return (
    <View style={[styles.container, isFullscreen && styles.fullscreenContainer]}>
      <StatusBar hidden={isFullscreen} />
      {showBackButton && (
        <SafeAreaView style={styles.backButtonContainer}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackPress}
            activeOpacity={0.7}
          >
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
        </SafeAreaView>
      )}
      <WebView
        ref={webViewRef}
        source={{ uri: playerUrl }}
        style={styles.webview}
        onMessage={handleWebViewMessage}
        injectedJavaScript={injectedJavaScript}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        allowsFullscreenVideo={true}
        allowsProtectedMedia={true}
        onError={(error) => { console.error('WebView error:', error); }}
        onHttpError={(error) => { console.error('WebView HTTP error:', error); }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  fullscreenContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  backButtonContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1001,
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  backButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignSelf: 'flex-start',
  },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  webview: {
    flex: 1,
  },
});

export default VideoPlayerScreen;

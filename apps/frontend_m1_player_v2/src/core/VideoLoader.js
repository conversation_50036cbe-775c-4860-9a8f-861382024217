import Hls from 'hls.js';

/**
 * VideoLoader - 统一的视频加载器，支持HLS和MP4
 * 负责处理不同格式视频的加载和播放
 */
export class VideoLoader {
  constructor() {
    this.hlsInstances = new Map();
    this.loadingPromises = new Map();
  }

  /**
   * 加载视频到指定的video元素
   * @param {HTMLVideoElement} videoElement - 视频元素
   * @param {string} videoUrl - 视频URL
   * @returns {Promise} 加载Promise
   */
  async loadVideo(videoElement, videoUrl) {
    if (!videoElement || !videoUrl) {
      return Promise.reject(new Error('Video element and URL are required'));
    }

    // 如果正在加载相同的URL，返回现有的Promise
    const loadingKey = `${videoElement.id || 'unnamed'}_${videoUrl}`;
    if (this.loadingPromises.has(loadingKey)) {
      return this.loadingPromises.get(loadingKey);
    }

    // 清理之前的实例
    this.cleanup(videoElement);

    let loadPromise;
    if (this.isHLSUrl(videoUrl)) {
      loadPromise = this.loadHLS(videoElement, videoUrl);
    } else {
      loadPromise = this.loadMP4(videoElement, videoUrl);
    }

    // 存储Promise用于去重
    this.loadingPromises.set(loadingKey, loadPromise);

    // 清理Promise缓存
    loadPromise.finally(() => {
      this.loadingPromises.delete(loadingKey);
    });

    return loadPromise;
  }

  /**
   * 判断是否为HLS URL
   * @param {string} url - 视频URL
   * @returns {boolean}
   */
  isHLSUrl(url) {
    return url && (url.includes('.m3u8') || url.includes('application/vnd.apple.mpegurl'));
  }

  /**
   * 加载HLS流
   * @param {HTMLVideoElement} videoElement - 视频元素
   * @param {string} hlsUrl - HLS URL
   * @returns {Promise}
   */
  loadHLS(videoElement, hlsUrl) {
    return new Promise((resolve, reject) => {
      if (Hls.isSupported()) {
        const hls = new Hls({
          enableWorker: true,
          lowLatencyMode: false,
          backBufferLength: 90,
          maxBufferLength: 60,
          maxMaxBufferLength: 600,
          capLevelToPlayerSize: true,
          // Recommended loader tuning can be added here if needed
          // liveSyncDurationCount: 3,
          // startLevel: -1, // auto
          debug: false,
        });

        // 存储HLS实例
        this.hlsInstances.set(videoElement, hls);

        // 设置事件监听
        this.setupHLSEventListeners(hls, videoElement, resolve, reject);

        // 加载源
        hls.loadSource(hlsUrl);
        hls.attachMedia(videoElement);

      } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari原生支持HLS
        console.log('Using native HLS support (Safari)');
        videoElement.src = hlsUrl;
        
        const handleLoadedData = () => {
          videoElement.removeEventListener('loadeddata', handleLoadedData);
          videoElement.removeEventListener('error', handleError);
          resolve();
        };

        const handleError = (error) => {
          videoElement.removeEventListener('loadeddata', handleLoadedData);
          videoElement.removeEventListener('error', handleError);
          reject(new Error(`Native HLS load failed: ${error.message}`));
        };

        videoElement.addEventListener('loadeddata', handleLoadedData);
        videoElement.addEventListener('error', handleError);
        
      } else {
        reject(new Error('HLS is not supported in this browser'));
      }
    });
  }

  /**
   * 设置HLS事件监听器
   * @param {Hls} hls - HLS实例
   * @param {HTMLVideoElement} videoElement - 视频元素（未直接使用）
   * @param {Function} resolve - Promise resolve
   * @param {Function} reject - Promise reject
   */
  setupHLSEventListeners(hls, _videoElement, resolve, reject) {
    let resolved = false;

    hls.on(Hls.Events.MANIFEST_PARSED, () => {
      if (!resolved) {
        resolved = true;
        console.log('HLS manifest parsed successfully');
        resolve();
      }
    });

    hls.on(Hls.Events.ERROR, (event, data) => {
      console.error('HLS Error:', data);
      
      if (data.fatal) {
        if (!resolved) {
          resolved = true;
          reject(new Error(`HLS fatal error: ${data.type} - ${data.details}`));
        } else {
          this.handleFatalError(hls, data, _videoElement);
        }
      }
    });

    hls.on(Hls.Events.MEDIA_ATTACHED, () => {
      console.log('HLS media attached');
    });
  }

  /**
   * Optionally cap auto level to improve stability at high playback rates
   */
  setAutoLevelCapForRate(videoElement, playbackRate) {
    const hls = this.hlsInstances.get(videoElement);
    if (!hls) {return;}

    // If rate >= 2, cap one or two levels below current estimate to avoid rebuffer
    try {
      const levels = hls.levels || [];
      if (!levels.length) {return;}

      if (playbackRate >= 3.0) {
        // Cap to a conservative mid/low level
        hls.autoLevelCapping = Math.max(0, Math.floor(levels.length / 3));
      } else if (playbackRate >= 2.0) {
        // Cap slightly below the highest
        hls.autoLevelCapping = Math.max(0, levels.length - 2);
      } else {
        // Remove cap for normal speed
        hls.autoLevelCapping = -1; // -1 means no capping
      }
      // Force ABR reevaluation
      if (typeof hls.nextAutoLevel !== 'undefined') {
        hls.nextAutoLevel = hls.autoLevelCapping;
      }
    } catch (e) {
      console.warn('Failed to set auto level capping:', e);
    }
  }

  /**
   * 处理HLS致命错误
   * @param {Hls} hls - HLS实例
   * @param {Object} data - 错误数据
   * @param {HTMLVideoElement} videoElement - 视频元素
   */
  handleFatalError(hls, data, videoElement) {
    switch (data.type) {
      case Hls.ErrorTypes.NETWORK_ERROR:
        console.log('HLS Network error, trying to recover...');
        hls.startLoad();
        break;
      case Hls.ErrorTypes.MEDIA_ERROR:
        console.log('HLS Media error, trying to recover...');
        hls.recoverMediaError();
        break;
      default:
        console.error('HLS Unrecoverable error, destroying instance');
        this.cleanup(videoElement);
        break;
    }
  }

  /**
   * 加载MP4视频
   * @param {HTMLVideoElement} videoElement - 视频元素
   * @param {string} mp4Url - MP4 URL
   * @returns {Promise}
   */
  loadMP4(videoElement, mp4Url) {
    return new Promise((resolve, reject) => {
      const handleLoadedData = () => {
        videoElement.removeEventListener('loadeddata', handleLoadedData);
        videoElement.removeEventListener('error', handleError);
        console.log('MP4 video loaded successfully');
        resolve();
      };

      const handleError = (error) => {
        videoElement.removeEventListener('loadeddata', handleLoadedData);
        videoElement.removeEventListener('error', handleError);
        reject(new Error(`MP4 load failed: ${error.message}`));
      };

      videoElement.addEventListener('loadeddata', handleLoadedData);
      videoElement.addEventListener('error', handleError);
      
      videoElement.src = mp4Url;
      videoElement.load();
    });
  }

  /**
   * 清理指定视频元素的HLS实例
   * @param {HTMLVideoElement} videoElement - 视频元素
   */
  cleanup(videoElement) {
    const hls = this.hlsInstances.get(videoElement);
    if (hls) {
      try {
        hls.destroy();
        console.log('HLS instance destroyed');
      } catch (error) {
        console.warn('Error destroying HLS instance:', error);
      }
      this.hlsInstances.delete(videoElement);
    }
  }

  /**
   * 销毁所有HLS实例
   */
  destroy() {
    console.log('Destroying all HLS instances...');
    this.hlsInstances.forEach((hls, _videoElement) => {
      try {
        hls.destroy();
      } catch (error) {
        console.warn('Error destroying HLS instance:', error);
      }
    });
    this.hlsInstances.clear();
    this.loadingPromises.clear();
  }

  /**
   * 获取当前活跃的HLS实例数量
   * @returns {number}
   */
  getActiveInstancesCount() {
    return this.hlsInstances.size;
  }
}

// 创建全局单例
export const videoLoader = new VideoLoader();

// 页面卸载时清理资源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    videoLoader.destroy();
  });
}

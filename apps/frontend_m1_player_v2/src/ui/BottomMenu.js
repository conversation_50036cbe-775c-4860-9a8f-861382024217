/**
 * Creates and manages the bottom sheet menu for video options.
 */
export class BottomMenu {
  constructor(container, player) {
    this.container = container;
    this.player = player;
    this.menuElement = null;
    this.backdropElement = null;
    this._createMenu();
  }

  _createMenu() {
    this.backdropElement = document.createElement('div');
    this.backdropElement.className = 'bottom-sheet-backdrop';

    this.menuElement = document.createElement('div');
    this.menuElement.className = 'bottom-sheet';

    this.menuElement.innerHTML = `
      <div class="sheet-handle"></div>
      <div class="sheet-content">
        <div class="sheet-row">
          <span>Mute</span>
          <div class="toggle-switch mute-toggle">
            <div class="toggle-knob"></div>
          </div>
        </div>
        <div class="sheet-row-group">
          <span>Playback Speed <span class="current-speed">1.0X</span></span>
          <div class="speed-options">
            <button data-speed="0.5">0.5X</button>
            <button data-speed="0.75">0.75X</button>
            <button data-speed="1.0" class="active">1.0X</button>
            <button data-speed="1.25">1.25X</button>
            <button data-speed="1.5">1.5X</button>
            <button data-speed="2.0">2.0X</button>
          </div>
        </div>
        <div class="sheet-row">
          <span>Block Post</span>
        </div>
        <div class="sheet-row">
          <span>Playback Feedback</span>
        </div>
        <div class="sheet-row">
          <span>Contact us</span>
        </div>
      </div>
    `;

    this.backdropElement.appendChild(this.menuElement);
    this.container.appendChild(this.backdropElement);

    this._addEventListeners();
  }

  _addEventListeners() {
    // Save references to handlers for later removal
    this.handleBackdropClick = this.hide.bind(this);
    this.handleMuteToggle = this._handleMuteToggle.bind(this);
    this.handleSpeedChange = this._handleSpeedChange.bind(this);

    // Mute toggle
    this.menuElement.querySelector('.mute-toggle').addEventListener('click', this.handleMuteToggle);

    // Playback speed
    this.menuElement.querySelector('.speed-options').addEventListener('click', this.handleSpeedChange);

    // Prevent clicks on the menu itself from propagating to the backdrop
    this.handleMenuClick = (e) => e.stopPropagation();
    this.menuElement.addEventListener('pointerdown', this.handleMenuClick);
  }

  _handleMuteToggle(e) {
    e.stopPropagation(); // Prevent click from bubbling up
    const isMuted = this.player.toggleMute();
    e.currentTarget.classList.toggle('active', isMuted);
  }

  _handleSpeedChange(e) {
    e.stopPropagation(); // Prevent click from bubbling up
    if (e.target.tagName === 'BUTTON') {
      const speed = parseFloat(e.target.dataset.speed);
      this.player.setPlaybackRate(speed);

      // Update UI
      this.menuElement.querySelector('.speed-options .active').classList.remove('active');
      e.target.classList.add('active');
      this.menuElement.querySelector('.current-speed').textContent = `${speed.toFixed(1)}X`;
    }
  }

  show() {
    // Sync UI with current player state before showing
    const currentMuteState = this.player.isMuted();
    this.menuElement.querySelector('.mute-toggle').classList.toggle('active', currentMuteState);

    const currentRate = this.player.getPlaybackRate();
    const currentSpeedSpan = this.menuElement.querySelector('.current-speed');
    const speedButtons = this.menuElement.querySelectorAll('.speed-options button');
    
    speedButtons.forEach(button => {
        const buttonSpeed = parseFloat(button.dataset.speed);
        button.classList.toggle('active', buttonSpeed === currentRate);
    });

    if (currentSpeedSpan) {
        currentSpeedSpan.textContent = `${currentRate.toFixed(1)}X`;
    }

    this.backdropElement.classList.add('visible');
    this.menuElement.classList.add('visible');

    // Add the listener slightly delayed to avoid capturing the 'pressup' event
    this.backdropElement.addEventListener('pointerdown', this.handleBackdropClick);
  }

  hide() {
    this.backdropElement.classList.remove('visible');
    this.menuElement.classList.remove('visible');
    // Immediately remove the listener on hide
    this.backdropElement.removeEventListener('pointerdown', this.handleBackdropClick);
  }

  destroy() {
    // Ensure listener is removed on destroy, just in case
    this.backdropElement.removeEventListener('pointerdown', this.handleBackdropClick);
    this.menuElement.removeEventListener('pointerdown', this.handleMenuClick);
    this.menuElement.querySelector('.mute-toggle').removeEventListener('click', this.handleMuteToggle);
    this.menuElement.querySelector('.speed-options').removeEventListener('click', this.handleSpeedChange);
    
    // Remove elements from DOM to prevent memory leaks
    if (this.backdropElement.parentElement) {
      this.container.removeChild(this.backdropElement);
    }
    if (this.menuElement.parentElement) {
      this.container.removeChild(this.menuElement);
    }
  }
} 
/* Styles for the video feed */
.video-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: black;
  display: flex;
  align-items: center;
  justify-content: center;
  will-change: transform;
}

.video-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  will-change: transform;
}

.video-player {
  position: relative;
  z-index: 0; /* keep video below UI overlays */
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-height: 100%;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10; /* Ensure it's above the hardware-accelerated video layer */
  width: clamp(60px, 18vw, 100px); /* Responsive icon size */
  height: clamp(60px, 18vw, 100px); /* Responsive icon size */
  opacity: 0.8;
  pointer-events: none; /* So it doesn't interfere with other gestures */
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0;
  pointer-events: none;
  transition: opacity 200ms ease-in-out;
  z-index: 1; /* Below the icon and indicators */
}

.video-overlay.visible {
  opacity: 1;
}

.seek-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border-radius: 10px;
  /* Make font size and padding responsive */
  font-size: clamp(20px, 5vw, 30px);
  padding: clamp(8px, 2vh, 15px) clamp(15px, 4vw, 25px);
  z-index: 20;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  pointer-events: none;
}

.seek-indicator.active {
  opacity: 1;
} 
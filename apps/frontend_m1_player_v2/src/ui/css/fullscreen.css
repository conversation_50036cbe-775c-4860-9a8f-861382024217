/* --- Fullscreen Mode Styles --- */

/* 
  This class is added to the main #app container when in fullscreen.
  It acts as a global switch for all fullscreen-related styles.
*/
.fullscreen-mode {
  /* Future styles for the container in fullscreen will go here */
}

/* 
  This class can be added to the body to prevent scrolling on the main
  React Native view, if needed.
*/
.h5-in-fullscreen {
  overflow: hidden; /* Prevent body scrolling */
}

/*
  In fullscreen, we might want to hide some elements by default.
  For example, the non-fullscreen sidebar.
*/
.fullscreen-mode .video-sidebar {
  display: none;
}
.fullscreen-mode .video-info {
  display: none;
}
.fullscreen-mode .progress-container {
  display: none;
}
.fullscreen-mode .house-drawer-container {
  display: none;
}
.fullscreen-mode .play-icon {
  display: none !important;
}

/* Hide non-fullscreen client badge in fullscreen */
.fullscreen-mode .video-item > .client-badge {
  display: none !important;
}

/* Controls visibility */
.fullscreen-mode .fullscreen-controls {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease-in-out;
}

/* visible state controlled by FullscreenController.toggleControls */
.fullscreen-mode .fullscreen-controls.visible {
  opacity: 1;
  pointer-events: auto;
}

.fullscreen-mode .house-drawer-toggle {
  display: none;
}

/* --- Fullscreen UI Elements --- */


.fs-top-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 1.5vw;
  align-items: center;
  padding: 1vh 3vw;
  background: linear-gradient(to bottom, rgba(0,0,0,0.5), transparent);
  height: 12vh;
}

/* Place client badge inside top bar in fullscreen */
.fullscreen-mode .fs-top-bar .client-badge {
  position: static;
  display: flex;
  margin-left: auto;
  top: auto;
  right: auto;
  background: rgba(0,0,0,0.25);
  z-index: auto;
}


.fs-back-button img {
    width: clamp(20px, 4vw, 24px);
    height: clamp(20px, 4vw, 24px);
    display: block;
  }

.fs-video-title {
  color: white;
  font-size: clamp(16px, 3vw, 20px);
  font-weight: bold;
  margin-left: 1vw;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 0 1 60%;
  min-width: 0;
}

.fs-bottom-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2vh 3vw;
  background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);
  display: flex;
  flex-direction: column;
  gap: 1.5vh;
}

/* --- Fullscreen Progress Bar --- */
.fs-progress-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.fs-seek-bar {
  flex-grow: 1;
  height: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  touch-action: none; 
}

.fs-seek-bar-track {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.fs-seek-bar-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0; 
  background-color: white;
  border-radius: 2px;
}

.fs-seek-bar-thumb {
  position: absolute;
  top: 50%;
  left: 0;
  width: 12px;
  height: 12px;
  background-color: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.fs-time-display {
  color: white;
  font-size: clamp(12px, 2.5vw, 14px);
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.6);
  white-space: nowrap;
}

/* --- Fullscreen Action Buttons --- */
.fs-actions-container {
  display: flex;
  
  align-items: center;
  gap: 6vw;
}

.fs-action-button {
  cursor: pointer;
}

.fs-action-button img {
  width: clamp(22px, 4vw, 28px);
  height: clamp(22px, 4vw, 28px);
  display: block;
}

.fs-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 50;
  cursor: pointer;
}

.fs-play-button img {
  width: clamp(60px, 15vw, 80px);
  height: clamp(60px, 15vw, 80px);
  display: block;
  filter: drop-shadow(0 0 10px rgba(0,0,0,0.5));
} 
/* 
  Styles for the video information overlay (title and description).
*/

.video-info {
  position: absolute;
  bottom: 8vh; /* Responsive: Leave 5% of viewport height for progress bar */
  left: 0;
  right: 18vw;
  padding: 0vh 0vw 2vh 4vw; /* top/bottom, right, left. Right padding avoids sidebar */
  box-sizing: border-box;
  color: white;
  pointer-events: none; /* Allow clicks to pass through to the video by default */
  z-index: 55; /* Base z-index for the info container */
  background: rgba(0, 0, 0, 0);
}

.video-info > * {
  pointer-events: auto; /* Re-enable pointer events for children like the 'more' button */
}

/* --- Client Badge (non-fullscreen) --- */
.client-badge {
  position: absolute;
  top: 10vh;
  right: 2vw;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  /* removed outer border per request */
  border-radius: 999px;
  color: #fff;
  background: rgba(0,0,0,0.25);
  z-index: 95; /* below sidebar(100), above overlays */
}
.client-badge .client-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  object-fit: cover;
}
.client-badge .client-name {
  font-weight: 600;
  font-size: 14px;
}
.client-badge .client-contact {
  color: #fff;
  background: transparent;
  border: 2px solid #fff;
  border-radius: 999px;
  padding: 2px 10px;
  font-weight: 600;
}

.video-title {
  font-size: clamp(16px, 4.5vw, 22px); /* Responsive font size */
  font-weight: bold;
  line-height: 1.4;
  
  /* Truncate to 2 lines */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.video-description-wrapper {
  margin-top: 1vh; /* Responsive margin */
  font-size: clamp(14px, 3.8vw, 18px); /* Responsive font size */
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
}

.video-description {
  /* Truncate to a single line by default */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* --- Expanded State --- */
.video-description-wrapper.expanded .video-description {
  white-space: normal;
  word-break: break-word;
  
}

.more-toggle {
  color: #87ceeb; /* A nice blue color */
  font-weight: bold;
  cursor: pointer;
  padding-left: 5px;
  flex-shrink: 0; /* Prevent the button from being shrunk by flexbox */
} 